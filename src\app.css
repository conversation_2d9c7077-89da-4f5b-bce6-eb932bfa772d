/* Import Inter font */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
      "Helvetica Neue", Arial, sans-serif;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  .btn-primary {
    @apply btn bg-slate-700 text-white hover:bg-slate-800 focus:ring-slate-500/20 shadow-sm hover:shadow-md border border-slate-700 hover:border-slate-800 transition-all duration-200;
    background: linear-gradient(135deg, rgb(51, 65, 85), rgb(30, 41, 59));
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, rgb(30, 41, 59), rgb(15, 23, 42));
    transform: translateY(-1px);
  }

  .btn-secondary {
    @apply btn bg-white/95 text-slate-700 border border-slate-300 hover:bg-slate-50 hover:border-slate-400 focus:ring-slate-500/20 shadow-sm hover:shadow-md backdrop-blur-sm transition-all duration-200;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.98),
      rgba(248, 250, 252, 0.95)
    );
  }

  .btn-secondary:hover {
    transform: translateY(-1px);
  }

  .btn-ghost {
    @apply btn text-slate-600 hover:text-slate-800 hover:bg-slate-100 focus:ring-slate-500/20 transition-all duration-200;
  }

  .btn-ghost:hover {
    transform: translateY(-1px);
  }

  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm hover:shadow-md;
  }

  /* Card Components */
  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 transition-shadow duration-200 hover:shadow-md;
  }

  .card-body {
    @apply px-6 py-4;
  }

  /* Enhanced Card Variants */
  .card-elevated {
    @apply bg-white rounded-xl shadow-md border border-gray-100 hover:shadow-lg transition-all duration-200;
  }

  .card-interactive {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md hover:border-gray-300 transition-all duration-200 cursor-pointer;
  }

  /* Form Components */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200;
  }

  /* Badge Components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply badge bg-blue-100 text-blue-800;
  }

  .badge-secondary {
    @apply badge bg-gray-100 text-gray-800;
  }

  .badge-success {
    @apply badge bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }

  .badge-danger {
    @apply badge bg-red-100 text-red-800;
  }

  /* Filter Tabs */
  .filter-tabs {
    @apply flex flex-wrap gap-2;
  }

  .filter-tab {
    @apply px-4 py-2 text-sm font-medium rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }

  .filter-tab:not(.active) {
    @apply bg-white text-gray-600 border-gray-300 hover:bg-gray-50 hover:text-gray-900 hover:border-gray-400;
  }

  .filter-tab.active {
    @apply bg-blue-600 text-white border-blue-600 shadow-sm;
  }

  /* Action Buttons */
  .action-btn {
    @apply p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }

  .action-btn-danger {
    @apply action-btn hover:text-red-600 hover:bg-red-50;
  }

  /* Table Components */
  .table-container {
    @apply card overflow-hidden;
  }

  .table {
    @apply w-full divide-y divide-gray-200;
  }

  .table-header {
    @apply bg-gray-50;
  }

  .table-header th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }

  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }

  .table-row {
    @apply hover:bg-gray-50 transition-colors duration-150;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }

  /* Mobile Card */
  .mobile-card {
    @apply card mb-4 p-4 space-y-3;
  }

  .mobile-card-row {
    @apply flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0;
  }

  .mobile-card-label {
    @apply text-xs font-medium text-gray-500 uppercase tracking-wider;
  }

  .mobile-card-value {
    @apply text-sm text-gray-900 font-medium;
  }

  /* Mobile Navigation Enhancements */
  .mobile-nav-item {
    @apply flex flex-col items-center justify-center px-4 py-3 rounded-xl text-xs font-medium transition-all duration-200;
  }

  .mobile-nav-item.active {
    @apply text-blue-600 bg-blue-50;
  }

  .mobile-nav-item:not(.active) {
    @apply text-gray-600 hover:text-blue-600 hover:bg-gray-50 active:bg-gray-100;
  }

  /* Professional Button Enhancements */
  .btn-professional {
    @apply inline-flex items-center justify-center px-4 py-2.5 text-sm font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm hover:shadow;
  }

  .btn-professional-primary {
    @apply btn-professional bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 border border-blue-600;
  }

  .btn-professional-secondary {
    @apply btn-professional bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:border-gray-400 focus:ring-blue-500;
  }

  /* Utility Classes */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
