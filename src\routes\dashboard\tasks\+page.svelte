<script lang="ts">
  import { goto } from '$app/navigation';
  import { invalidateAll } from '$app/navigation';
  import { onMount } from 'svelte';
  import type { PageData } from './$types';
  import { fade, fly } from 'svelte/transition';
  import { flip } from 'svelte/animate';
  import { cubicOut, quintOut } from 'svelte/easing';
  import Button from '$lib/components/ui/Button.svelte';
  import Badge from '$lib/components/ui/Badge.svelte';
  import TaskCard from '$lib/components/TaskCard.svelte';
  import PageTransition from '$lib/components/ui/PageTransition.svelte';
  import TaskListSkeleton from '$lib/components/ui/TaskListSkeleton.svelte';
  import FloatingActionButton from '$lib/components/ui/FloatingActionButton.svelte';
  import { toasts } from '$lib/stores/toast';
  import { loading, withLoading } from '$lib/stores/loading';

  export let data: PageData;

  let filter = 'all'; // 'all', 'active', 'completed', 'today', 'overdue'
  let searchQuery = '';
  let categoryFilter: string[] = []; // Selected category IDs for filtering
  let showAdvancedFilter = false; // Toggle for advanced filter panel
  let showDeleteModal = false;
  let taskToDelete: string | null = null;
  let deletingTask = false;

  // Animation configuration for smooth task transitions
  const flipDuration = 450;
  const flyDuration = 250;
  const strikethroughDelay = 300; // Delay before moving task to completed section

  // Page visibility and sync management
  let lastSyncTime = Date.now();
  let isPageVisible = true;

  // Create reactive variables for tasks to support optimistic updates
  let localTasks = [...data.tasks.all];
  let togglingTasks = new Set<string>();
  let strikethroughTasks = new Set<string>(); // Track tasks showing strikethrough animation
  let pendingOperations = new Map<string, any>(); // Track pending operations for sync

  // Update local tasks when data changes (from server)
  $: {
    // Only update if we don't have pending operations to avoid flashing
    if (togglingTasks.size === 0) {
      localTasks = [...data.tasks.all];
    } else {
      // Merge server data with local optimistic updates carefully
      const serverTasks = [...data.tasks.all];

      // For each server task, check if we have a pending operation
      const updatedTasks = serverTasks.map(serverTask => {
        const pendingOp = pendingOperations.get(serverTask.id);
        if (pendingOp && togglingTasks.has(serverTask.id)) {
          // Keep optimistic state while operation is pending
          return pendingOp.optimisticTask;
        }
        return serverTask;
      });

      // Only update if there are actual changes to avoid unnecessary re-renders
      const hasChanges = updatedTasks.some((task, index) => {
        const currentTask = localTasks[index];
        return !currentTask || task.id !== currentTask.id || task.completed !== currentTask.completed;
      });

      if (hasChanges) {
        localTasks = updatedTasks;
      }
    }
  }

  // Handle page visibility changes for smart syncing
  onMount(() => {
    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible') {
        isPageVisible = true;
        const timeSinceLastSync = Date.now() - lastSyncTime;

        // If page was hidden for more than 30 seconds, sync with server
        if (timeSinceLastSync > 30000) {
          console.log('Page became visible after', timeSinceLastSync, 'ms, syncing...');
          await invalidateAll();
          lastSyncTime = Date.now();
        }
      } else {
        isPageVisible = false;
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  });

  // Clean up stale pending operations (safety mechanism)
  function cleanupStaleOperations() {
    const now = Date.now();
    const staleThreshold = 30000; // 30 seconds

    for (const [taskId, operation] of pendingOperations.entries()) {
      if (now - operation.timestamp > staleThreshold) {
        console.warn(`Cleaning up stale operation for task ${taskId}`);
        pendingOperations.delete(taskId);
        togglingTasks.delete(taskId);
      }
    }

    // Trigger reactivity
    pendingOperations = pendingOperations;
    togglingTasks = togglingTasks;
  }

  // Run cleanup periodically
  onMount(() => {
    const cleanupInterval = setInterval(cleanupStaleOperations, 60000); // Every minute

    return () => {
      clearInterval(cleanupInterval);
    };
  });

  // Split tasks into incomplete and completed based on filter
  $: incompleteTasks = localTasks.filter(task => {
    // Apply search filter first
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (!task.title.toLowerCase().includes(query) &&
          !task.subtitle?.toLowerCase().includes(query) &&
          !task.notes?.toLowerCase().includes(query)) {
        return false;
      }
    }

    // Apply category filter
    if (categoryFilter.length > 0) {
      if (!categoryFilter.includes(task.categoryId || '')) {
        return false;
      }
    }

    // Apply status filter for incomplete tasks
    switch (filter) {
      case 'active':
        return !task.completed;
      case 'completed':
        return false; // Show no incomplete tasks when filter is completed
      case 'today':
        return data.tasks.today.some(t => t.id === task.id) && !task.completed;
      case 'overdue':
        return data.tasks.overdue.some(t => t.id === task.id) && !task.completed;
      default:
        return !task.completed;
    }
  }).sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()); // Sort by creation date, newest first

  $: completedTasks = localTasks.filter(task => {
    // Apply search filter first
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (!task.title.toLowerCase().includes(query) &&
          !task.subtitle?.toLowerCase().includes(query) &&
          !task.notes?.toLowerCase().includes(query)) {
        return false;
      }
    }

    // Apply category filter
    if (categoryFilter.length > 0) {
      if (!categoryFilter.includes(task.categoryId || '')) {
        return false;
      }
    }

    // Apply status filter for completed tasks
    switch (filter) {
      case 'active':
        return false; // Show no completed tasks when filter is active
      case 'completed':
        return task.completed;
      case 'today':
        return data.tasks.today.some(t => t.id === task.id) && task.completed;
      case 'overdue':
        return data.tasks.overdue.some(t => t.id === task.id) && task.completed;
      default:
        return task.completed;
    }
  }).sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()); // Sort by creation date, newest first

  function formatDate(dateString: string | null): string {
    if (!dateString) return 'No due date';
    
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    if (date.toDateString() === today.toDateString()) {
      return `Today at ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}`;
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return `Tomorrow at ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}`;
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit'
      });
    }
  }

  function getPriorityLabel(priority: number): string {
    switch (priority) {
      case 3: return 'High';
      case 2: return 'Normal';
      case 1: return 'Low';
      default: return 'Normal';
    }
  }

  function getPriorityVariant(priority: number): 'danger' | 'warning' | 'primary' {
    switch (priority) {
      case 3: return 'danger';
      case 2: return 'warning';
      case 1: return 'primary';
      default: return 'warning';
    }
  }

  function getCategoryName(categoryId: string | null): string {
    if (!categoryId) return '';
    const category = data.categories.find(c => c.id === categoryId);
    return category?.name || '';
  }

  function getCategoryColor(categoryId: string | null): string {
    if (!categoryId) return '#64748b'; // secondary-500 fallback
    const category = data.categories.find(c => c.id === categoryId);
    return category?.color || '#64748b';
  }

  function hexToRgba(hex: string, alpha: number = 1): string {
    const h = hex?.trim()?.replace('#', '') ?? '';
    if (h.length !== 6) return `rgba(100,116,139,${alpha})`;
    const r = parseInt(h.slice(0, 2), 16);
    const g = parseInt(h.slice(2, 4), 16);
    const b = parseInt(h.slice(4, 6), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  }

  // Category filter functions
  function toggleCategoryFilter(categoryId: string) {
    if (categoryFilter.includes(categoryId)) {
      categoryFilter = categoryFilter.filter(id => id !== categoryId);
    } else {
      categoryFilter = [...categoryFilter, categoryId];
    }
  }

  function clearAllFilters() {
    filter = 'all';
    categoryFilter = [];
    searchQuery = '';
  }

  // Get active filter count for display
  $: activeFilterCount = (filter !== 'all' ? 1 : 0) + categoryFilter.length;

  function isOverdue(dateString: string | null): boolean {
    if (!dateString) return false;
    const date = new Date(dateString);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const taskDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    return taskDate.getTime() < today.getTime();
  }

  function isDueSoon(dateString: string | null): boolean {
    if (!dateString) return false;
    const now = new Date();
    const due = new Date(dateString);
    // within next 24h and not overdue
    return due.getTime() >= now.getTime() && (due.getTime() - now.getTime()) <= 24 * 60 * 60 * 1000;
  }

  async function toggleTask(taskId: string, completed: boolean) {
    // Prevent double-click
    if (togglingTasks.has(taskId)) return;

    togglingTasks.add(taskId);
    togglingTasks = togglingTasks; // Trigger reactivity

    // Find the task to toggle
    const taskIndex = localTasks.findIndex(t => t.id === taskId);
    if (taskIndex === -1) {
      togglingTasks.delete(taskId);
      togglingTasks = togglingTasks;
      return;
    }

    const originalTask = localTasks[taskIndex];
    const isCompleting = !completed; // If task was incomplete, we're completing it

    // If completing task, show strikethrough first
    if (isCompleting) {
      strikethroughTasks.add(taskId);
      strikethroughTasks = strikethroughTasks; // Trigger reactivity

      // Wait for strikethrough animation
      await new Promise(resolve => setTimeout(resolve, strikethroughDelay));
    }

    // Create optimistic task state
    const optimisticTask = {
      ...originalTask,
      completed: !completed,
      completedAt: !completed ? new Date().toISOString() : null
    };

    // Store pending operation for sync tracking
    pendingOperations.set(taskId, {
      originalTask,
      optimisticTask,
      timestamp: Date.now()
    });

    // Update local array optimistically (this triggers the move animation)
    localTasks[taskIndex] = optimisticTask;
    localTasks = [...localTasks]; // Trigger reactivity

    // Remove strikethrough state since task is now moving
    strikethroughTasks.delete(taskId);
    strikethroughTasks = strikethroughTasks;

    try {
      let response;
      if (!completed) {
        // Complete the task
        response = await fetch(`/api/tasks/${taskId}/complete`, {
          method: 'POST'
        });
      } else {
        // Uncomplete the task
        response = await fetch(`/api/tasks/${taskId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ completed: false, completedAt: null })
        });
      }

      if (response.ok) {
        // Parse response to get updated task data
        const responseData = await response.json();
        const serverTask = responseData.task;

        if (serverTask) {
          // Compare server response with our optimistic update
          const currentTaskIndex = localTasks.findIndex(t => t.id === taskId);
          if (currentTaskIndex !== -1) {
            // Update with server data (authoritative) after a small delay to let animation complete
            setTimeout(() => {
              const latestIndex = localTasks.findIndex(t => t.id === taskId);
              if (latestIndex !== -1) {
                localTasks[latestIndex] = serverTask;
                localTasks = [...localTasks];
              }
            }, 100);
          }
        }

        // Show success toast
        const taskName = originalTask.title;
        if (!completed) {
          toasts.success('Task completed!', `"${taskName}" has been marked as complete.`);
        } else {
          toasts.success('Task reopened!', `"${taskName}" has been marked as incomplete.`);
        }

        // Clean up pending operation after animation
        setTimeout(() => {
          pendingOperations.delete(taskId);
        }, flyDuration + 50);

        // Sync with server for other potential changes
        await invalidateAll();
      } else {
        // Revert optimistic update on failure
        console.error('Failed to toggle task:', response.status, response.statusText);
        const currentTaskIndex = localTasks.findIndex(t => t.id === taskId);
        if (currentTaskIndex !== -1) {
          localTasks[currentTaskIndex] = originalTask;
          localTasks = [...localTasks];
        }
        pendingOperations.delete(taskId);

        // Show error toast
        toasts.error('Failed to update task', 'Please try again or check your connection.');
      }
    } catch (error) {
      console.error('Error toggling task:', error);
      // Revert optimistic update on error
      const currentTaskIndex = localTasks.findIndex(t => t.id === taskId);
      if (currentTaskIndex !== -1) {
        localTasks[currentTaskIndex] = originalTask;
        localTasks = [...localTasks];
      }
      pendingOperations.delete(taskId);

      // Show error toast
      toasts.error('Network error', 'Unable to update task. Please check your connection.');
    } finally {
      togglingTasks.delete(taskId);
      togglingTasks = togglingTasks; // Trigger reactivity
    }
  }

  function confirmDeleteTask(taskId: string) {
    taskToDelete = taskId;
    showDeleteModal = true;
  }

  function cancelDelete() {
    showDeleteModal = false;
    taskToDelete = null;
  }

  async function deleteTask() {
    if (!taskToDelete) return;

    // Get task name for toast message
    const task = localTasks.find(t => t.id === taskToDelete);
    const taskName = task?.title || 'Task';

    deletingTask = true;
    try {
      const response = await fetch(`/api/tasks/${taskToDelete}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        await invalidateAll();
        showDeleteModal = false;
        taskToDelete = null;

        // Show success toast
        toasts.success('Task deleted!', `"${taskName}" has been permanently deleted.`);
      } else {
        console.error('Failed to delete task');
        toasts.error('Failed to delete task', 'Please try again or check your connection.');
      }
    } catch (error) {
      console.error('Error deleting task:', error);
      toasts.error('Network error', 'Unable to delete task. Please check your connection.');
    } finally {
      deletingTask = false;
    }
  }
</script>

<svelte:head>
  <title>Tasks - Routine Mail</title>
</svelte:head>

<PageTransition>
<!-- Desktop Header -->
<div class="mb-8 hidden md:block">
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
    <div>
      <h1 class="text-2xl font-bold text-slate-900">Tasks</h1>
      <p class="text-slate-600 mt-1">Manage your tasks and routines</p>
    </div>
    <Button href="/dashboard/tasks/new" variant="primary">
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
      </svg>
      New Task
    </Button>
  </div>
</div>

<!-- Mobile: No header, more space for tasks -->

<!-- Desktop Search and Filters - Compact Professional Design -->
<div class="hidden md:block mb-4">
  <div class="bg-slate-50/80 backdrop-blur-sm rounded-xl border border-slate-200/60 shadow-sm p-4">
    <!-- Compact Search and Filter Row -->
    <div class="flex items-center gap-3 mb-3">
      <!-- Search Box -->
      <div class="flex-1 relative">
        <input
          type="text"
          placeholder="Search tasks..."
          bind:value={searchQuery}
          class="w-full pl-9 pr-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-slate-500/20 focus:border-slate-400 bg-white hover:border-slate-400 transition-all duration-200 placeholder-slate-500"
        />
        <svg class="absolute left-2.5 top-2.5 h-3.5 w-3.5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>

      <!-- Active Filter Indicator -->
      {#if activeFilterCount > 0 || searchQuery}
        <div class="flex items-center gap-2 px-2.5 py-1.5 bg-slate-100 text-slate-700 rounded-md text-xs font-medium">
          <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z" />
          </svg>
          {#if searchQuery && activeFilterCount > 0}
            Search + {activeFilterCount} filter{activeFilterCount > 1 ? 's' : ''}
          {:else if searchQuery}
            Search active
          {:else}
            {activeFilterCount} filter{activeFilterCount > 1 ? 's' : ''} active
          {/if}
          <button
            class="ml-1 p-0.5 hover:bg-slate-200 rounded transition-colors"
            on:click={clearAllFilters}
            title="Clear all filters and search"
            aria-label="Clear all filters and search"
          >
            <svg class="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      {/if}
    </div>

    <!-- Compact Filter Sections -->
    <div class="space-y-2.5">
      <!-- Clear All Button (Desktop) -->
      {#if activeFilterCount > 0 || searchQuery}
        <div class="flex justify-end">
          <button
            class="px-3 py-1.5 text-xs font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-md transition-all duration-200 flex items-center gap-1.5"
            on:click={clearAllFilters}
            title="Clear all filters and search"
          >
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
            Clear All
          </button>
        </div>
      {/if}

      <!-- Status Filters -->
      <div>
        <div class="flex flex-wrap gap-1.5">
          <button
            class="px-3 py-1.5 text-xs font-medium rounded-md border transition-all duration-200 {filter === 'all' ? 'bg-slate-700 text-white border-slate-700' : 'bg-white text-slate-600 border-slate-300 hover:border-slate-400 hover:bg-slate-50'}"
            on:click={() => filter = 'all'}
          >
            All ({data.tasks.all.length})
          </button>
          <button
            class="px-3 py-1.5 text-xs font-medium rounded-md border transition-all duration-200 {filter === 'active' ? 'bg-slate-700 text-white border-slate-700' : 'bg-white text-slate-600 border-slate-300 hover:border-slate-400 hover:bg-slate-50'}"
            on:click={() => filter = 'active'}
          >
            Active ({data.tasks.active.length})
          </button>
          <button
            class="px-3 py-1.5 text-xs font-medium rounded-md border transition-all duration-200 {filter === 'today' ? 'bg-slate-700 text-white border-slate-700' : 'bg-white text-slate-600 border-slate-300 hover:border-slate-400 hover:bg-slate-50'}"
            on:click={() => filter = 'today'}
          >
            Today ({data.tasks.today.length})
          </button>
          <button
            class="px-3 py-1.5 text-xs font-medium rounded-md border transition-all duration-200 {filter === 'overdue' ? 'bg-red-600 text-white border-red-600' : 'bg-white text-slate-600 border-slate-300 hover:border-slate-400 hover:bg-slate-50'}"
            on:click={() => filter = 'overdue'}
          >
            Overdue ({data.tasks.overdue.length})
          </button>
          <button
            class="px-3 py-1.5 text-xs font-medium rounded-md border transition-all duration-200 {filter === 'completed' ? 'bg-emerald-600 text-white border-emerald-600' : 'bg-white text-slate-600 border-slate-300 hover:border-slate-400 hover:bg-slate-50'}"
            on:click={() => filter = 'completed'}
          >
            Done ({data.tasks.completed.length})
          </button>
        </div>
      </div>

      <!-- Category Filters -->
      {#if data.categories.length > 0}
        <div>
          <div class="flex flex-wrap gap-1.5">
            {#each data.categories as category}
              <button
                class="flex items-center px-2.5 py-1.5 text-xs font-medium rounded-md border transition-all duration-200 {categoryFilter.includes(category.id) ? 'bg-white text-slate-800 border-slate-400 shadow-sm' : 'bg-white text-slate-600 border-slate-300 hover:border-slate-400 hover:bg-slate-50'}"
                on:click={() => toggleCategoryFilter(category.id)}
                style={categoryFilter.includes(category.id) ? `border-color: ${category.color}; box-shadow: 0 2px 4px ${category.color}15;` : ''}
              >
                <div
                  class="w-2 h-2 rounded-full mr-1.5 flex-shrink-0"
                  style="background-color: {category.color}"
                ></div>
                {category.name}
              </button>
            {/each}
          </div>
        </div>
      {/if}
    </div>
  </div>
</div>

<!-- Mobile Compact Search and Advanced Filter -->
<div class="md:hidden mb-4">
  <!-- Search Bar with Filter Button -->
  <div class="px-3 mb-3">
    <div class="flex gap-2">
      <!-- Search Input -->
      <div class="flex-1 relative">
        <input
          type="text"
          placeholder="Search tasks..."
          bind:value={searchQuery}
          class="w-full pl-10 pr-4 py-2.5 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-sm transition-all duration-200 placeholder-gray-500"
        />
        <svg class="absolute left-3 top-3 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>

      <!-- Filter Toggle Button -->
      <div class="flex gap-1">
        <button
          class="flex items-center justify-center px-3 py-2.5 text-sm font-medium rounded-lg border transition-all duration-200 relative {showAdvancedFilter ? 'bg-slate-700 text-white border-slate-700 shadow-sm' : 'bg-white text-slate-600 border-slate-300 hover:bg-slate-50'}"
          on:click={() => showAdvancedFilter = !showAdvancedFilter}
        >
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z" />
          </svg>
          {#if activeFilterCount > 0 || searchQuery}
            <span class="ml-1 px-1.5 py-0.5 text-xs bg-red-500 text-white rounded-full min-w-[18px] text-center">
              {searchQuery ? (activeFilterCount > 0 ? activeFilterCount + 1 : 1) : activeFilterCount}
            </span>
          {:else}
            Filter
          {/if}
        </button>

        <!-- Quick Clear Button (Mobile) -->
        {#if (activeFilterCount > 0 || searchQuery) && !showAdvancedFilter}
          <button
            class="flex items-center justify-center px-2 py-2.5 text-sm font-medium rounded-lg border border-slate-300 bg-white text-slate-600 hover:bg-slate-50 transition-all duration-200"
            on:click={clearAllFilters}
            title="Clear all filters and search"
            aria-label="Clear all filters and search"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        {/if}
      </div>
    </div>
  </div>

  <!-- Advanced Filter Panel -->
  {#if showAdvancedFilter}
    <div class="px-3 mb-3 bg-slate-50 rounded-lg mx-3 py-3 border border-slate-200 advanced-filter-panel">
      <!-- Header with Close Button -->
      <div class="flex items-center justify-between mb-3">
        <h3 class="text-sm font-semibold text-slate-800">Advanced Filters</h3>
        <button
          class="p-1 hover:bg-slate-200 rounded-md transition-colors duration-200"
          on:click={() => showAdvancedFilter = false}
          aria-label="Close filters"
        >
          <svg class="w-4 h-4 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Status Filters -->
      <div class="mb-3">
        <h4 class="text-xs font-semibold text-slate-700 mb-2 uppercase tracking-wide">Status</h4>
        <div class="flex flex-wrap gap-1.5">
          <button
            class="px-3 py-1.5 text-xs font-medium rounded-md border transition-all duration-200 {filter === 'all' ? 'bg-slate-700 text-white border-slate-700' : 'bg-white text-slate-600 border-slate-300 hover:bg-slate-50'}"
            on:click={() => filter = 'all'}
          >
            All ({data.tasks.all.length})
          </button>
          <button
            class="px-3 py-1.5 text-xs font-medium rounded-md border transition-all duration-200 {filter === 'active' ? 'bg-slate-700 text-white border-slate-700' : 'bg-white text-slate-600 border-slate-300 hover:bg-slate-50'}"
            on:click={() => filter = 'active'}
          >
            Active ({data.tasks.active.length})
          </button>
          <button
            class="px-3 py-1.5 text-xs font-medium rounded-md border transition-all duration-200 {filter === 'today' ? 'bg-slate-700 text-white border-slate-700' : 'bg-white text-slate-600 border-slate-300 hover:bg-slate-50'}"
            on:click={() => filter = 'today'}
          >
            Today ({data.tasks.today.length})
          </button>
          <button
            class="px-3 py-1.5 text-xs font-medium rounded-md border transition-all duration-200 {filter === 'overdue' ? 'bg-red-600 text-white border-red-600' : 'bg-white text-slate-600 border-slate-300 hover:bg-slate-50'}"
            on:click={() => filter = 'overdue'}
          >
            Overdue ({data.tasks.overdue.length})
          </button>
          <button
            class="px-3 py-1.5 text-xs font-medium rounded-md border transition-all duration-200 {filter === 'completed' ? 'bg-emerald-600 text-white border-emerald-600' : 'bg-white text-slate-600 border-slate-300 hover:bg-slate-50'}"
            on:click={() => filter = 'completed'}
          >
            Done ({data.tasks.completed.length})
          </button>
        </div>
      </div>

      <!-- Category Filters -->
      {#if data.categories.length > 0}
        <div class="mb-3">
          <h4 class="text-xs font-semibold text-slate-700 mb-2 uppercase tracking-wide">Categories</h4>
          <div class="flex flex-wrap gap-1.5">
            {#each data.categories as category}
              <button
                class="flex items-center px-2.5 py-1.5 text-xs font-medium rounded-md border transition-all duration-200 {categoryFilter.includes(category.id) ? 'bg-white text-slate-800 border-slate-400 shadow-sm' : 'bg-white text-slate-600 border-slate-300 hover:bg-slate-50'}"
                on:click={() => toggleCategoryFilter(category.id)}
                style={categoryFilter.includes(category.id) ? `border-color: ${category.color}; box-shadow: 0 0 0 1px ${category.color}20;` : ''}
              >
                <div
                  class="w-2 h-2 rounded-full mr-1.5 flex-shrink-0"
                  style="background-color: {category.color}"
                ></div>
                {category.name}
              </button>
            {/each}
          </div>
        </div>
      {/if}

      <!-- Clear All Button (Mobile) -->
      {#if activeFilterCount > 0 || searchQuery}
        <div class="pt-3 border-t border-slate-200">
          <button
            class="w-full px-3 py-2 text-xs font-medium text-slate-600 hover:text-slate-800 bg-slate-50 hover:bg-slate-100 rounded-md transition-all duration-200 flex items-center justify-center gap-1.5"
            on:click={clearAllFilters}
            title="Clear all filters and search"
          >
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
            Clear All {searchQuery ? 'Filters & Search' : 'Filters'}
          </button>
        </div>
      {/if}
    </div>
  {/if}
</div>

{#if incompleteTasks.length === 0 && completedTasks.length === 0}
  <div class="text-center py-12">
    <svg class="mx-auto h-12 w-12 text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
    </svg>
    <h3 class="mt-2 text-sm font-medium text-slate-900">
      {#if searchQuery}
        No tasks found
      {:else}
        No tasks yet
      {/if}
    </h3>
    <p class="mt-1 text-sm text-slate-500">
      {#if searchQuery}
        No tasks found matching "{searchQuery}"
      {:else}
        Get started by creating your first task.
      {/if}
    </p>
    {#if !searchQuery}
      <div class="mt-6">
        <Button href="/dashboard/tasks/new" variant="primary">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          Create a Task
        </Button>
      </div>
    {/if}
  </div>
{:else}
  <!-- Mobile View: Split Layout with Task Cards -->
  <div class="block md:hidden px-3 mobile-task-container">
    <!-- Incomplete Tasks -->
    {#if incompleteTasks.length > 0}
      <div class="space-y-1.5 mb-4">
        {#each incompleteTasks as task (task.id)}
          <div
            animate:flip={{ duration: flipDuration, easing: quintOut }}
            in:fly={{ y: -30, duration: flyDuration, easing: cubicOut }}
            out:fly={{ y: 30, duration: flyDuration, easing: cubicOut }}
          >
            <TaskCard
              {task}
              categories={data.categories}
              compact={true}
              isToggling={togglingTasks.has(task.id)}
              isStrikethrough={strikethroughTasks.has(task.id)}
              on:toggle={(e) => toggleTask(e.detail, task.completed)}
              on:edit={(e) => goto(`/dashboard/tasks/${e.detail}/edit`)}
              on:delete={(e) => confirmDeleteTask(e.detail)}
            />
          </div>
        {/each}
      </div>
    {/if}

    <!-- Completed Tasks Section -->
    {#if completedTasks.length > 0}
      <div class="completed-section">
        <details open>
          <summary>
            Completed ({completedTasks.length})
            <div class="divider"></div>
          </summary>
          <div class="space-y-1.5 mt-2">
            {#each completedTasks as task (task.id)}
              <div
                animate:flip={{ duration: flipDuration, easing: quintOut }}
                in:fly={{ y: 30, duration: flyDuration, easing: cubicOut }}
                out:fly={{ y: -30, duration: flyDuration, easing: cubicOut }}
              >
                <TaskCard
                  {task}
                  categories={data.categories}
                  compact={true}
                  isToggling={togglingTasks.has(task.id)}
                  isStrikethrough={strikethroughTasks.has(task.id)}
                  on:toggle={(e) => toggleTask(e.detail, task.completed)}
                  on:edit={(e) => goto(`/dashboard/tasks/${e.detail}/edit`)}
                  on:delete={(e) => confirmDeleteTask(e.detail)}
                />
              </div>
            {/each}
          </div>
        </details>
      </div>
    {/if}
  </div>

  <!-- Desktop View: Split Tables -->
  <div class="hidden md:block">
    <!-- Incomplete Tasks Table -->
    {#if incompleteTasks.length > 0}
      <div class="table-container mb-6">
        <table class="table enhanced-table">
          <thead class="table-header">
            <tr>
              <th class="w-12"></th>
              <th>Task</th>
              <th>Priority</th>
              <th>Due Date</th>
              <th>Category</th>
              <th class="w-24">Actions</th>
            </tr>
          </thead>
          <tbody class="table-body">
            {#each incompleteTasks as task (task.id)}
              <tr
                class="table-row enhanced-row cursor-pointer hover:bg-slate-50/40 hover:shadow-sm"
                animate:flip={{ duration: flipDuration, easing: quintOut }}
                in:fly={{ y: -20, duration: flyDuration, easing: cubicOut }}
                out:fly={{ y: 20, duration: flyDuration, easing: cubicOut }}
                role="button"
                tabindex="0"
                aria-label={`Open task ${task.title}`}
                on:click={() => goto(`/dashboard/tasks/${task.id}`)}
                on:keydown={(e) => (e.key === 'Enter' || e.key === ' ') && goto(`/dashboard/tasks/${task.id}`)}
              >
                <td class="table-cell" on:click|stopPropagation>
                  <input
                    type="checkbox"
                    class="enhanced-checkbox"
                    checked={task.completed}
                    disabled={togglingTasks.has(task.id)}
                    on:change|stopPropagation={() => toggleTask(task.id, task.completed)}
                  />
                </td>
                <td class="table-cell">
                  <div class="block hover:text-slate-700 transition-colors">
                    <div class="font-medium text-slate-900" class:line-through={task.completed || strikethroughTasks.has(task.id)} class:strikethrough-animation={strikethroughTasks.has(task.id)}>
                      {task.title}
                    </div>
                    {#if task.subtitle}
                      <div class="text-sm text-slate-500 mt-1" class:line-through={task.completed || strikethroughTasks.has(task.id)}>{task.subtitle}</div>
                    {/if}
                  </div>
                </td>
                <td class="table-cell">
                  <Badge variant={getPriorityVariant(task.priority)} size="sm">
                    {getPriorityLabel(task.priority)}
                  </Badge>
                </td>
                <td class="table-cell text-slate-600">
                  <div class="flex items-center gap-1.5">
                    <svg class="w-4 h-4 {isOverdue(task.dueDate) ? 'text-red-600' : isDueSoon(task.dueDate) ? 'text-yellow-600' : 'text-slate-500'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span class="{isOverdue(task.dueDate) ? 'text-red-600 font-medium' : isDueSoon(task.dueDate) ? 'text-yellow-700 font-medium' : ''}">{formatDate(task.dueDate)}</span>
                  </div>
                </td>
                <td class="table-cell">
                  {#if getCategoryName(task.categoryId)}
                    <Badge size="sm" class="badge-secondary" style={`background-color: ${hexToRgba(getCategoryColor(task.categoryId), 0.15)}; color: ${getCategoryColor(task.categoryId)};`}>
                      {getCategoryName(task.categoryId)}
                    </Badge>
                  {/if}
                </td>
                <td class="table-cell" on:click|stopPropagation>
                  <div class="flex gap-1">
                    <button
                      class="action-btn"
                      on:click|stopPropagation={() => goto(`/dashboard/tasks/${task.id}/edit`)}
                      title="Edit Task"
                      aria-label="Edit task: {task.title}"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                    </button>
                    <button
                      class="action-btn-danger"
                      on:click|stopPropagation={() => confirmDeleteTask(task.id)}
                      title="Delete Task"
                      aria-label="Delete task: {task.title}"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                    </button>
                  </div>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    {/if}

    <!-- Completed Tasks Table -->
    {#if completedTasks.length > 0}
      <div class="completed-section">
        <details open>
          <summary>
            Completed ({completedTasks.length})
            <div class="divider"></div>
          </summary>
          <div class="table-container mt-3">
            <table class="table enhanced-table">
              <thead class="table-header">
                <tr>
                  <th class="w-12"></th>
                  <th>Task</th>
                  <th>Priority</th>
                  <th>Due Date</th>
                  <th>Category</th>
                  <th class="w-24">Actions</th>
                </tr>
              </thead>
              <tbody class="table-body">
                {#each completedTasks as task (task.id)}
                  <tr
                    class="table-row enhanced-row opacity-70 cursor-pointer hover:bg-emerald-50/30 hover:shadow-sm"
                    animate:flip={{ duration: flipDuration, easing: quintOut }}
                    in:fly={{ y: 20, duration: flyDuration, easing: cubicOut }}
                    out:fly={{ y: -20, duration: flyDuration, easing: cubicOut }}
                    role="button"
                    tabindex="0"
                    aria-label={`Open task ${task.title}`}
                    on:click={() => goto(`/dashboard/tasks/${task.id}`)}
                    on:keydown={(e) => (e.key === 'Enter' || e.key === ' ') && goto(`/dashboard/tasks/${task.id}`)}
                  >
                    <td class="table-cell" on:click|stopPropagation>
                      <input
                        type="checkbox"
                        class="enhanced-checkbox"
                        checked={task.completed}
                        disabled={togglingTasks.has(task.id)}
                        on:change|stopPropagation={() => toggleTask(task.id, task.completed)}
                      />
                    </td>
                    <td class="table-cell">
                      <div class="block hover:text-slate-700 transition-colors">
                        <div class="font-medium text-slate-900 line-through" class:strikethrough-animation={strikethroughTasks.has(task.id)}>
                          {task.title}
                        </div>
                        {#if task.subtitle}
                          <div class="text-sm text-slate-500 mt-1 line-through">{task.subtitle}</div>
                        {/if}
                      </div>
                    </td>
                    <td class="table-cell">
                      <Badge variant={getPriorityVariant(task.priority)} size="sm">
                        {getPriorityLabel(task.priority)}
                      </Badge>
                    </td>
                    <td class="table-cell text-slate-600">
                      <div class="flex items-center gap-1.5">
                        <svg class="w-4 h-4 {isOverdue(task.dueDate) ? 'text-red-600' : isDueSoon(task.dueDate) ? 'text-yellow-600' : 'text-slate-500'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span class="{isOverdue(task.dueDate) ? 'text-red-600 font-medium' : isDueSoon(task.dueDate) ? 'text-yellow-700 font-medium' : ''}">{formatDate(task.dueDate)}</span>
                      </div>
                    </td>
                    <td class="table-cell">
                      {#if getCategoryName(task.categoryId)}
                        <Badge size="sm" class="badge-secondary" style={`background-color: ${hexToRgba(getCategoryColor(task.categoryId), 0.15)}; color: ${getCategoryColor(task.categoryId)};`}>
                          {getCategoryName(task.categoryId)}
                        </Badge>
                      {/if}
                    </td>
                    <td class="table-cell" on:click|stopPropagation>
                      <div class="flex gap-1">
                        <button
                          class="action-btn"
                          on:click|stopPropagation={() => goto(`/dashboard/tasks/${task.id}/edit`)}
                          title="Edit Task"
                          aria-label="Edit task: {task.title}"
                        >
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                        </button>
                        <button
                          class="action-btn-danger"
                          on:click|stopPropagation={() => confirmDeleteTask(task.id)}
                          title="Delete Task"
                          aria-label="Delete task: {task.title}"
                        >
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        </details>
      </div>
    {/if}
  </div>
{/if}

<!-- Delete Confirmation Modal -->
{#if showDeleteModal}
  <div
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    on:click={cancelDelete}
    on:keydown={(e) => e.key === 'Escape' && cancelDelete()}
    role="dialog"
    aria-modal="true"
    aria-labelledby="delete-modal-title"
    tabindex="0"
  >
    <div
      class="bg-white rounded-xl shadow-large max-w-md w-full"
      on:click|stopPropagation
      on:keydown|stopPropagation
      role="document"
    >
      <div class="px-6 py-4 border-b border-slate-200">
        <div class="flex items-center justify-between">
          <h3 id="delete-modal-title" class="text-lg font-semibold text-slate-900">Delete Task</h3>
          <button
            class="text-slate-400 hover:text-slate-600 transition-colors"
            on:click={cancelDelete}
            aria-label="Close delete confirmation dialog"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
      <div class="px-6 py-4">
        <p class="text-slate-600">Are you sure you want to delete this task? This action cannot be undone.</p>
      </div>
      <div class="px-6 py-4 border-t border-slate-200 flex gap-3 justify-end">
        <Button variant="secondary" on:click={cancelDelete} disabled={deletingTask}>
          Cancel
        </Button>
        <Button
          variant="danger"
          on:click={deleteTask}
          disabled={deletingTask}
          loading={deletingTask}
          loadingText="Deleting..."
        >
          Delete Task
        </Button>
      </div>
    </div>
  </div>
{/if}



<style>
  .compact-filter-tab {
    @apply px-3 py-1.5 text-xs font-medium rounded-full border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 transition-colors whitespace-nowrap flex-shrink-0;
  }

  .compact-filter-tab.active {
    @apply bg-slate-700 text-white border-slate-700 hover:bg-slate-800;
  }

  /* Enhanced Table Styling - Office Professional */
  .enhanced-table {
    @apply bg-white/95 backdrop-blur-sm rounded-xl border border-slate-200/60 shadow-sm overflow-hidden;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
  }

  .enhanced-table thead {
    @apply bg-gradient-to-r from-slate-50/90 to-slate-100/70 backdrop-blur-sm;
  }

  .enhanced-table th {
    @apply px-5 py-3 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider border-b border-slate-200/60;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  }

  .enhanced-row {
    @apply transition-all duration-200 border-b border-slate-100/60;
    backdrop-filter: blur(8px);
  }

  .enhanced-row:hover {
    @apply transform translate-x-0.5;
    box-shadow: 0 2px 8px rgba(51, 65, 85, 0.06);
  }

  .enhanced-row td {
    @apply px-5 py-3.5;
  }

  /* Mobile Task Container with Subtle Background */
  .mobile-task-container {
    position: relative;
  }

  .mobile-task-container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      rgba(248, 250, 252, 0.3) 0%,
      rgba(241, 245, 249, 0.2) 25%,
      rgba(248, 250, 252, 0.3) 50%,
      rgba(241, 245, 249, 0.2) 75%,
      rgba(248, 250, 252, 0.3) 100%);
    background-size: 400% 400%;
    animation: subtleGradient 25s ease infinite;
    pointer-events: none;
    z-index: -1;
  }

  @keyframes subtleGradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  /* Subtle decorative elements */
  .mobile-task-container::after {
    content: '';
    position: fixed;
    top: 15%;
    right: -8%;
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, rgba(51, 65, 85, 0.02) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
    z-index: -1;
    animation: float 20s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(180deg); }
  }

  /* Enhanced checkbox styling for desktop tables */
  .enhanced-checkbox {
    @apply h-4 w-4 text-slate-700 focus:ring-slate-500/20 border-slate-300 rounded transition-all duration-200 hover:border-slate-400;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .enhanced-checkbox:checked {
    @apply bg-slate-700 border-slate-700;
    box-shadow: 0 1px 3px rgba(51, 65, 85, 0.3);
  }

  /* Advanced Filter Panel Animation */
  .advanced-filter-panel {
    animation: slideDown 0.2s ease-out;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Completed Section Styling */
  .completed-section {
    margin-top: 1.5rem;
  }

  .completed-section summary {
    font-weight: 600;
    font-size: 1rem;
    color: #343a40;
    cursor: pointer;
    padding: 0.75rem 1.25rem;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .completed-section summary::marker {
    content: '';
  }

  .completed-section summary:before {
    content: '▶';
    font-size: 0.8em;
    transition: transform 0.2s;
  }

  .completed-section details[open] > summary:before {
    transform: rotate(90deg);
  }

  /* Animation performance optimizations */
  .task-list, .space-y-2 {
    contain: layout style;
    position: relative;
  }

  .task-row, .compact-task-card {
    will-change: transform, opacity;
    backface-visibility: hidden;
    transform: translateZ(0);
    position: relative;
  }

  .table-row {
    will-change: transform, opacity;
    backface-visibility: hidden;
    position: relative;
  }

  /* Smooth transitions for task state changes */
  .task-list > div, .space-y-2 > div {
    transition: transform 0.2s ease-out;
  }

  .divider {
    flex: 1;
    height: 1px;
    background-color: #dee2e6;
  }

  /* Mobile completed section adjustments */
  @media (max-width: 768px) {
    .completed-section summary {
      padding: 0.75rem 0;
      background-color: transparent;
      border: none;
      border-top: 1px solid #dee2e6;
    }
  }

  /* Strikethrough animation for desktop table view */
  .strikethrough-animation {
    position: relative;
    transition: color 0.3s ease;
    color: #9ca3af !important;
  }

  .strikethrough-animation::after {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 0;
    height: 2px;
    background-color: #9ca3af;
    animation: strikethrough 0.3s ease-out forwards;
  }

  @keyframes strikethrough {
    from {
      width: 0;
    }
    to {
      width: 100%;
    }
  }
</style>

<!-- Floating Action Button for Mobile -->
<FloatingActionButton
  href="/dashboard/tasks/new"
  ariaLabel="Create new task"
/>
</PageTransition>
