<script lang="ts">
  export let href: string;
  export let ariaLabel: string = 'Add new item';
</script>

<a
  {href}
  class="md:hidden fixed bottom-24 right-4 z-40 w-16 h-16 bg-white/95 backdrop-blur-sm hover:bg-white text-slate-700 hover:text-slate-800 rounded-2xl shadow-lg hover:shadow-xl border border-slate-200/60 hover:border-slate-300/60 transition-all duration-300 flex items-center justify-center active:scale-95 hover:-translate-y-1 group"
  aria-label={ariaLabel}
>
  <div class="relative">
    <!-- Plus Icon -->
    <svg class="w-6 h-6 transition-transform duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2.5">
      <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" />
    </svg>

    <!-- Subtle background glow -->
    <div class="absolute inset-0 bg-slate-100/50 rounded-xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
  </div>
</a>

<style>
  /* Enhanced floating button with subtle animations */
  a {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
  }

  a::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 1rem;
    background: linear-gradient(135deg, rgba(51, 65, 85, 0.02), rgba(51, 65, 85, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  a:hover::before {
    opacity: 1;
  }
</style>
